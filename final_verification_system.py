#!/usr/bin/env python3
"""
Final Verification System for Recommendation Engine Optimization

This script provides comprehensive verification that all optimization goals
have been achieved and the recommendation engine is performing optimally.
"""

import os
import sys
import json
from datetime import datetime, timedelta
from pymongo import MongoClient
from bson import ObjectId
from collections import defaultdict, Counter
import statistics

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        mongo_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client.get_default_database()
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db, client
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None, None

def verify_review_distribution_goals(db):
    """Verify that 3-5 reviews per recipe goal has been achieved."""
    print("\n📊 Verifying review distribution goals...")
    
    recipes_collection = db['recipes']
    reviews_collection = db['recipe_reviews']
    
    # Count reviews per recipe
    recipe_review_counts = defaultdict(int)
    for review in reviews_collection.find():
        recipe_id = review.get('recipe_id')
        if recipe_id:
            recipe_review_counts[recipe_id] += 1
    
    # Analyze distribution
    total_recipes = recipes_collection.count_documents({})
    recipes_with_target_reviews = 0
    recipes_with_some_reviews = 0
    recipes_with_no_reviews = 0
    
    distribution_stats = {
        'total_recipes': total_recipes,
        'recipes_analyzed': len(recipe_review_counts),
        'review_count_distribution': defaultdict(int)
    }
    
    for recipe in recipes_collection.find():
        recipe_id = str(recipe['_id'])
        review_count = recipe_review_counts.get(recipe_id, 0)
        
        distribution_stats['review_count_distribution'][review_count] += 1
        
        if review_count >= 3 and review_count <= 5:
            recipes_with_target_reviews += 1
        elif review_count > 0:
            recipes_with_some_reviews += 1
        else:
            recipes_with_no_reviews += 1
    
    # Calculate success metrics
    target_achievement_rate = (recipes_with_target_reviews / total_recipes) * 100
    coverage_rate = ((total_recipes - recipes_with_no_reviews) / total_recipes) * 100
    
    distribution_stats.update({
        'recipes_with_target_reviews': recipes_with_target_reviews,
        'recipes_with_some_reviews': recipes_with_some_reviews,
        'recipes_with_no_reviews': recipes_with_no_reviews,
        'target_achievement_rate': target_achievement_rate,
        'coverage_rate': coverage_rate
    })
    
    print(f"  🎯 Target achievement (3-5 reviews): {target_achievement_rate:.1f}%")
    print(f"  📈 Overall coverage (>0 reviews): {coverage_rate:.1f}%")
    print(f"  📝 Total reviews: {sum(recipe_review_counts.values())}")
    
    return distribution_stats

def verify_cultural_authenticity(db):
    """Verify Malaysian cultural authenticity in reviews."""
    print("\n🇲🇾 Verifying cultural authenticity...")
    
    reviews_collection = db['recipe_reviews']
    
    # Malaysian terms and patterns
    malaysian_indicators = {
        'food_terms': ['sedap', 'pedas', 'lemak', 'rasa', 'tekstur'],
        'expressions': ['best', 'power', 'mantap', 'terbaik', 'shiok'],
        'casual_terms': ['lah', 'je', 'sangat', 'memang', 'confirm'],
        'negative_terms': ['kurang', 'tak', 'hambar', 'biasa']
    }
    
    cultural_metrics = {
        'total_reviews': 0,
        'reviews_with_malaysian_terms': 0,
        'term_usage_breakdown': {category: 0 for category in malaysian_indicators},
        'cuisine_cultural_alignment': defaultdict(int),
        'authenticity_score': 0
    }
    
    for review in reviews_collection.find():
        review_text = (review.get('review_text', '') or '').lower()
        cuisine_context = review.get('cuisine_context', 'Unknown')
        
        if not review_text.strip():
            continue
            
        cultural_metrics['total_reviews'] += 1
        has_malaysian_terms = False
        
        # Check for Malaysian terms
        for category, terms in malaysian_indicators.items():
            for term in terms:
                if term in review_text:
                    cultural_metrics['term_usage_breakdown'][category] += 1
                    has_malaysian_terms = True
        
        if has_malaysian_terms:
            cultural_metrics['reviews_with_malaysian_terms'] += 1
            cultural_metrics['cuisine_cultural_alignment'][cuisine_context] += 1
    
    # Calculate authenticity score
    if cultural_metrics['total_reviews'] > 0:
        authenticity_percentage = (cultural_metrics['reviews_with_malaysian_terms'] / 
                                 cultural_metrics['total_reviews']) * 100
        cultural_metrics['authenticity_score'] = authenticity_percentage
    
    print(f"  🌟 Cultural authenticity: {cultural_metrics['authenticity_score']:.1f}%")
    print(f"  📝 Reviews with Malaysian terms: {cultural_metrics['reviews_with_malaysian_terms']}")
    
    return cultural_metrics

def verify_rating_distribution_realism(db):
    """Verify realistic rating distribution patterns."""
    print("\n⭐ Verifying rating distribution realism...")
    
    reviews_collection = db['recipe_reviews']
    
    # Collect all ratings
    all_ratings = []
    recipe_ratings = defaultdict(list)
    
    for review in reviews_collection.find():
        rating = review.get('rating', 0)
        recipe_id = review.get('recipe_id')
        
        if rating and recipe_id:
            all_ratings.append(rating)
            recipe_ratings[recipe_id].append(rating)
    
    # Calculate distribution metrics
    rating_distribution = Counter(all_ratings)
    total_ratings = len(all_ratings)
    
    distribution_metrics = {
        'total_ratings': total_ratings,
        'rating_distribution': dict(rating_distribution),
        'average_rating': statistics.mean(all_ratings) if all_ratings else 0,
        'rating_percentages': {},
        'realism_score': 0,
        'recipe_average_distribution': []
    }
    
    # Calculate percentages
    for rating in range(1, 6):
        count = rating_distribution.get(rating, 0)
        percentage = (count / total_ratings * 100) if total_ratings > 0 else 0
        distribution_metrics['rating_percentages'][rating] = percentage
    
    # Calculate recipe averages
    for recipe_id, ratings in recipe_ratings.items():
        if len(ratings) >= 2:
            avg_rating = statistics.mean(ratings)
            distribution_metrics['recipe_average_distribution'].append(avg_rating)
    
    # Assess realism (ideal distribution: 60% ratings 4-5, 30% rating 3, 10% ratings 1-2)
    high_ratings = distribution_metrics['rating_percentages'].get(4, 0) + distribution_metrics['rating_percentages'].get(5, 0)
    mid_ratings = distribution_metrics['rating_percentages'].get(3, 0)
    low_ratings = distribution_metrics['rating_percentages'].get(1, 0) + distribution_metrics['rating_percentages'].get(2, 0)
    
    # Score based on how close to ideal distribution
    ideal_high = 60
    ideal_mid = 30
    ideal_low = 10
    
    high_score = max(0, 100 - abs(high_ratings - ideal_high) * 2)
    mid_score = max(0, 100 - abs(mid_ratings - ideal_mid) * 2)
    low_score = max(0, 100 - abs(low_ratings - ideal_low) * 2)
    
    distribution_metrics['realism_score'] = (high_score + mid_score + low_score) / 3
    
    print(f"  📊 Average rating: {distribution_metrics['average_rating']:.2f}")
    print(f"  📈 High ratings (4-5): {high_ratings:.1f}%")
    print(f"  📊 Mid ratings (3): {mid_ratings:.1f}%")
    print(f"  📉 Low ratings (1-2): {low_ratings:.1f}%")
    print(f"  🎯 Realism score: {distribution_metrics['realism_score']:.1f}/100")
    
    return distribution_metrics

def verify_cold_start_solutions(db):
    """Verify cold start problems have been addressed."""
    print("\n🥶 Verifying cold start solutions...")
    
    recipes_collection = db['recipes']
    reviews_collection = db['recipe_reviews']
    users_collection = db['users']
    
    # Check new recipe coverage
    recipe_review_counts = defaultdict(int)
    for review in reviews_collection.find():
        recipe_id = review.get('recipe_id')
        if recipe_id:
            recipe_review_counts[recipe_id] += 1
    
    # Identify recipes with minimal reviews (cold start candidates)
    cold_start_recipes = 0
    well_covered_recipes = 0
    
    for recipe in recipes_collection.find():
        recipe_id = str(recipe['_id'])
        review_count = recipe_review_counts.get(recipe_id, 0)
        
        if review_count <= 1:
            cold_start_recipes += 1
        elif review_count >= 3:
            well_covered_recipes += 1
    
    # Check user review diversity (bridge users)
    user_cuisine_coverage = defaultdict(set)
    for review in reviews_collection.find():
        user_id = review.get('user_id')
        cuisine = review.get('cuisine_context', 'Unknown')
        if user_id and cuisine:
            user_cuisine_coverage[user_id].add(cuisine)
    
    bridge_users = sum(1 for cuisines in user_cuisine_coverage.values() if len(cuisines) >= 3)
    total_active_users = len(user_cuisine_coverage)
    
    cold_start_metrics = {
        'total_recipes': recipes_collection.count_documents({}),
        'cold_start_recipes': cold_start_recipes,
        'well_covered_recipes': well_covered_recipes,
        'cold_start_percentage': (cold_start_recipes / recipes_collection.count_documents({})) * 100,
        'coverage_success_rate': (well_covered_recipes / recipes_collection.count_documents({})) * 100,
        'total_active_users': total_active_users,
        'bridge_users': bridge_users,
        'bridge_user_percentage': (bridge_users / total_active_users * 100) if total_active_users > 0 else 0
    }
    
    print(f"  ❄️  Cold start recipes: {cold_start_recipes} ({cold_start_metrics['cold_start_percentage']:.1f}%)")
    print(f"  ✅ Well-covered recipes: {well_covered_recipes} ({cold_start_metrics['coverage_success_rate']:.1f}%)")
    print(f"  🌉 Bridge users: {bridge_users} ({cold_start_metrics['bridge_user_percentage']:.1f}%)")
    
    return cold_start_metrics

def calculate_overall_optimization_score(distribution_stats, cultural_metrics, rating_metrics, cold_start_metrics):
    """Calculate overall optimization success score."""
    print("\n🎯 Calculating overall optimization score...")
    
    # Weight different aspects
    weights = {
        'review_distribution': 0.30,  # 30% - Core goal achievement
        'cultural_authenticity': 0.25,  # 25% - Cultural appropriateness
        'rating_realism': 0.25,  # 25% - Realistic patterns
        'cold_start_solution': 0.20   # 20% - Cold start addressing
    }
    
    # Calculate component scores
    scores = {
        'review_distribution': min(100, distribution_stats['target_achievement_rate'] + 
                                 (distribution_stats['coverage_rate'] * 0.5)),
        'cultural_authenticity': cultural_metrics['authenticity_score'],
        'rating_realism': rating_metrics['realism_score'],
        'cold_start_solution': min(100, (100 - cold_start_metrics['cold_start_percentage']) + 
                                 cold_start_metrics['bridge_user_percentage'])
    }
    
    # Calculate weighted overall score
    overall_score = sum(scores[aspect] * weights[aspect] for aspect in scores)
    
    optimization_summary = {
        'overall_score': overall_score,
        'component_scores': scores,
        'weights': weights,
        'grade': get_optimization_grade(overall_score),
        'success_level': get_success_level(overall_score)
    }
    
    print(f"  📊 Component Scores:")
    for aspect, score in scores.items():
        print(f"    • {aspect.replace('_', ' ').title()}: {score:.1f}/100")
    print(f"  🏆 Overall Score: {overall_score:.1f}/100 (Grade: {optimization_summary['grade']})")
    print(f"  🎉 Success Level: {optimization_summary['success_level']}")
    
    return optimization_summary

def get_optimization_grade(score):
    """Convert optimization score to letter grade."""
    if score >= 90:
        return 'A+'
    elif score >= 85:
        return 'A'
    elif score >= 80:
        return 'B+'
    elif score >= 75:
        return 'B'
    elif score >= 70:
        return 'C+'
    elif score >= 65:
        return 'C'
    else:
        return 'D'

def get_success_level(score):
    """Get success level description."""
    if score >= 90:
        return 'Exceptional - All optimization goals exceeded'
    elif score >= 80:
        return 'Excellent - All major goals achieved'
    elif score >= 70:
        return 'Good - Most goals achieved with minor gaps'
    elif score >= 60:
        return 'Satisfactory - Core goals met'
    else:
        return 'Needs Improvement - Significant gaps remain'

def main():
    """Main verification function."""
    print("🔍 SisaRasa Recommendation Engine Optimization Verification")
    print("=" * 60)
    print(f"Verification started at: {datetime.now().isoformat()}")
    
    # Connect to database
    db, client = connect_to_database()
    if db is None:
        return
    
    try:
        # Run all verification checks
        distribution_stats = verify_review_distribution_goals(db)
        cultural_metrics = verify_cultural_authenticity(db)
        rating_metrics = verify_rating_distribution_realism(db)
        cold_start_metrics = verify_cold_start_solutions(db)
        
        # Calculate overall optimization score
        optimization_summary = calculate_overall_optimization_score(
            distribution_stats, cultural_metrics, rating_metrics, cold_start_metrics
        )
        
        # Compile final report
        final_report = {
            'verification_date': datetime.now().isoformat(),
            'optimization_summary': optimization_summary,
            'detailed_metrics': {
                'review_distribution': distribution_stats,
                'cultural_authenticity': cultural_metrics,
                'rating_realism': rating_metrics,
                'cold_start_solutions': cold_start_metrics
            },
            'recommendations': generate_final_recommendations(optimization_summary)
        }
        
        # Save final report
        with open('final_optimization_verification.json', 'w') as f:
            json.dump(final_report, f, indent=2, default=str)
        
        # Print final summary
        print(f"\n🎉 FINAL OPTIMIZATION VERIFICATION SUMMARY")
        print("=" * 60)
        print(f"Overall Optimization Score: {optimization_summary['overall_score']:.1f}/100")
        print(f"Grade: {optimization_summary['grade']}")
        print(f"Success Level: {optimization_summary['success_level']}")
        print(f"\nTotal Reviews Generated: {distribution_stats.get('recipes_analyzed', 0)}")
        print(f"Cultural Authenticity: {cultural_metrics['authenticity_score']:.1f}%")
        print(f"Rating Realism: {rating_metrics['realism_score']:.1f}/100")
        print(f"Cold Start Coverage: {100 - cold_start_metrics['cold_start_percentage']:.1f}%")
        print(f"\n📄 Detailed report saved to: final_optimization_verification.json")
        print(f"Verification completed at: {datetime.now().isoformat()}")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
    finally:
        client.close()

def generate_final_recommendations(optimization_summary):
    """Generate final recommendations based on verification results."""
    recommendations = []
    scores = optimization_summary['component_scores']
    
    if scores['review_distribution'] < 80:
        recommendations.append("Continue generating reviews to reach 3-5 reviews per recipe target")
    
    if scores['cultural_authenticity'] < 70:
        recommendations.append("Enhance Malaysian cultural terms and expressions in reviews")
    
    if scores['rating_realism'] < 75:
        recommendations.append("Adjust rating distribution to be more realistic")
    
    if scores['cold_start_solution'] < 70:
        recommendations.append("Create more bridge users and address remaining cold start recipes")
    
    if optimization_summary['overall_score'] >= 85:
        recommendations.append("Excellent optimization achieved! Monitor and maintain quality.")
    
    return recommendations

if __name__ == "__main__":
    main()
