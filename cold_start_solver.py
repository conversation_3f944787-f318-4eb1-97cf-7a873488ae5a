#!/usr/bin/env python3
"""
Cold Start Problem Solver

This script implements solutions for cold start problems in the recommendation engine:
1. New recipe cold start - ensure new recipes get initial reviews
2. New user cold start - create diverse review histories for new users
3. Gateway recipes - popular dishes that help establish user preferences
4. Preference pattern establishment for better recommendations
"""

import os
import sys
import json
import random
from datetime import datetime, timed<PERSON>ta
from pymongo import MongoClient
from bson import ObjectId
from collections import defaultdict

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        mongo_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client.get_default_database()
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db, client
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None, None

def identify_gateway_recipes(db):
    """Identify and create gateway recipes for new user onboarding."""
    print("\n🚪 Identifying gateway recipes...")
    
    recipes_collection = db['recipes']
    reviews_collection = db['recipe_reviews']
    
    # Get recipes with good review coverage
    recipe_review_counts = defaultdict(int)
    recipe_avg_ratings = defaultdict(list)
    
    for review in reviews_collection.find():
        recipe_id = review.get('recipe_id')
        rating = review.get('rating', 0)
        if recipe_id:
            recipe_review_counts[recipe_id] += 1
            recipe_avg_ratings[recipe_id].append(rating)
    
    # Calculate average ratings
    for recipe_id in recipe_avg_ratings:
        ratings = recipe_avg_ratings[recipe_id]
        recipe_avg_ratings[recipe_id] = sum(ratings) / len(ratings)
    
    # Find gateway recipe candidates
    gateway_candidates = []
    
    for recipe in recipes_collection.find():
        recipe_id = str(recipe['_id'])
        review_count = recipe_review_counts.get(recipe_id, 0)
        avg_rating = recipe_avg_ratings.get(recipe_id, 0)
        
        # Gateway criteria: 3+ reviews, 3.5+ rating, popular cuisines
        if (review_count >= 3 and 
            avg_rating >= 3.5 and 
            recipe.get('cuisine') in ['Malay', 'Chinese', 'Indian', 'International']):
            
            gateway_candidates.append({
                'recipe_id': recipe_id,
                'name': recipe.get('name', 'Unknown'),
                'cuisine': recipe.get('cuisine', 'Unknown'),
                'review_count': review_count,
                'avg_rating': avg_rating,
                'difficulty': recipe.get('difficulty', 'Medium'),
                'gateway_score': review_count * avg_rating  # Simple scoring
            })
    
    # Sort by gateway score and select top candidates
    gateway_candidates.sort(key=lambda x: x['gateway_score'], reverse=True)
    gateway_recipes = gateway_candidates[:20]  # Top 20 gateway recipes
    
    print(f"  🎯 Identified {len(gateway_recipes)} gateway recipes")
    
    # Categorize by cuisine for balanced onboarding
    gateway_by_cuisine = defaultdict(list)
    for recipe in gateway_recipes:
        gateway_by_cuisine[recipe['cuisine']].append(recipe)
    
    return gateway_recipes, dict(gateway_by_cuisine)

def create_new_user_onboarding_strategy(gateway_recipes):
    """Create onboarding strategy for new users."""
    print("\n👤 Creating new user onboarding strategy...")
    
    # Define onboarding archetypes
    onboarding_archetypes = [
        {
            'name': 'malaysian_traditional',
            'description': 'Users interested in traditional Malaysian cuisine',
            'preferred_cuisines': ['Malay', 'Chinese', 'Indian'],
            'rating_tendency': 'positive',  # Tends to rate traditional food highly
            'review_count_range': (4, 7),
            'gateway_preference': ['Malay', 'Chinese', 'Indian']
        },
        {
            'name': 'international_explorer',
            'description': 'Users who enjoy trying international dishes',
            'preferred_cuisines': ['International', 'Western', 'Fusion'],
            'rating_tendency': 'balanced',
            'review_count_range': (3, 6),
            'gateway_preference': ['International', 'Malay']
        },
        {
            'name': 'spicy_food_lover',
            'description': 'Users who love spicy and flavorful dishes',
            'preferred_cuisines': ['Malay', 'Indian', 'Thai'],
            'rating_tendency': 'spicy_positive',
            'review_count_range': (5, 8),
            'gateway_preference': ['Malay', 'Indian']
        },
        {
            'name': 'health_conscious',
            'description': 'Users focused on healthy eating',
            'preferred_cuisines': ['International', 'Chinese'],
            'rating_tendency': 'health_focused',
            'review_count_range': (3, 5),
            'gateway_preference': ['Chinese', 'International']
        },
        {
            'name': 'comfort_food_seeker',
            'description': 'Users who prefer familiar, comforting dishes',
            'preferred_cuisines': ['Malay', 'Chinese', 'International'],
            'rating_tendency': 'comfort_positive',
            'review_count_range': (4, 6),
            'gateway_preference': ['Malay', 'Chinese']
        }
    ]
    
    print(f"  📋 Created {len(onboarding_archetypes)} onboarding archetypes")
    
    return onboarding_archetypes

def generate_new_user_reviews(db, user_id, archetype, gateway_by_cuisine):
    """Generate initial reviews for a new user based on their archetype."""
    from malaysian_review_generator import (
        get_recipe_context, select_persona_for_recipe, 
        generate_review_content, MALAYSIAN_PERSONAS
    )
    
    # Determine number of reviews to generate
    min_reviews, max_reviews = archetype['review_count_range']
    num_reviews = random.randint(min_reviews, max_reviews)
    
    # Select gateway recipes based on archetype preferences
    available_recipes = []
    for cuisine in archetype['gateway_preference']:
        if cuisine in gateway_by_cuisine:
            available_recipes.extend(gateway_by_cuisine[cuisine][:3])  # Top 3 per cuisine
    
    # Add some random recipes for diversity
    all_gateway_recipes = []
    for recipes in gateway_by_cuisine.values():
        all_gateway_recipes.extend(recipes)
    
    additional_recipes = random.sample(all_gateway_recipes, min(3, len(all_gateway_recipes)))
    available_recipes.extend(additional_recipes)
    
    # Remove duplicates
    seen_ids = set()
    unique_recipes = []
    for recipe in available_recipes:
        if recipe['recipe_id'] not in seen_ids:
            unique_recipes.append(recipe)
            seen_ids.add(recipe['recipe_id'])
    
    # Select recipes for this user
    selected_recipes = random.sample(unique_recipes, min(num_reviews, len(unique_recipes)))
    
    # Generate reviews
    user_reviews = []
    for recipe_data in selected_recipes:
        # Get full recipe data
        recipes_collection = db['recipes']
        recipe = recipes_collection.find_one({'_id': ObjectId(recipe_data['recipe_id'])})
        
        if recipe:
            recipe_context = get_recipe_context(recipe)
            persona = select_persona_for_recipe(recipe_context)
            
            # Generate rating based on archetype tendency
            rating = generate_archetype_rating(archetype, recipe_context)
            
            # Generate review content
            review_text = generate_review_content(rating, recipe_context, persona)
            
            # Generate realistic timestamp (spread over past 1-3 months)
            days_ago = random.randint(7, 90)
            hours_ago = random.randint(0, 23)
            created_at = datetime.now() - timedelta(days=days_ago, hours=hours_ago)
            
            user_review = {
                'recipe_id': recipe_data['recipe_id'],
                'user_id': user_id,
                'rating': rating,
                'review_text': review_text,
                'created_at': created_at,
                'updated_at': created_at,
                'helpful_count': random.randint(0, 2),
                'verified_purchase': True,
                'cuisine_context': recipe_context['cuisine'],
                'difficulty_rating': random.randint(max(1, rating-1), min(5, rating+1)),
                'is_onboarding_review': True,
                'user_archetype': archetype['name']
            }
            
            user_reviews.append(user_review)
    
    return user_reviews

def generate_archetype_rating(archetype, recipe_context):
    """Generate rating based on user archetype and recipe context."""
    base_rating = 3.5  # Neutral base
    
    tendency = archetype['rating_tendency']
    cuisine = recipe_context['cuisine']
    is_spicy = recipe_context['is_spicy']
    
    # Adjust based on archetype
    if tendency == 'positive':
        if cuisine in archetype['preferred_cuisines']:
            base_rating += random.uniform(0.5, 1.5)
        else:
            base_rating += random.uniform(-0.5, 0.5)
    
    elif tendency == 'spicy_positive':
        if is_spicy:
            base_rating += random.uniform(0.8, 1.5)
        else:
            base_rating += random.uniform(-0.3, 0.7)
    
    elif tendency == 'health_focused':
        # Assume international and Chinese are healthier
        if cuisine in ['International', 'Chinese']:
            base_rating += random.uniform(0.3, 1.0)
        else:
            base_rating += random.uniform(-0.5, 0.5)
    
    elif tendency == 'comfort_positive':
        if cuisine in ['Malay', 'Chinese']:
            base_rating += random.uniform(0.5, 1.2)
        else:
            base_rating += random.uniform(-0.2, 0.8)
    
    else:  # balanced
        base_rating += random.uniform(-0.5, 1.0)
    
    # Convert to integer rating (1-5)
    rating = max(1, min(5, round(base_rating)))
    
    return rating

def solve_new_recipe_cold_start(db, gateway_by_cuisine):
    """Ensure new recipes get initial reviews to solve cold start."""
    print("\n🆕 Solving new recipe cold start...")
    
    recipes_collection = db['recipes']
    reviews_collection = db['recipe_reviews']
    users_collection = db['users']
    
    # Find recipes with 0-1 reviews (cold start recipes)
    recipe_review_counts = defaultdict(int)
    for review in reviews_collection.find():
        recipe_id = review.get('recipe_id')
        if recipe_id:
            recipe_review_counts[recipe_id] += 1
    
    cold_start_recipes = []
    for recipe in recipes_collection.find():
        recipe_id = str(recipe['_id'])
        review_count = recipe_review_counts.get(recipe_id, 0)
        
        if review_count <= 1:  # Cold start threshold
            cold_start_recipes.append({
                'recipe_id': recipe_id,
                'recipe': recipe,
                'current_reviews': review_count
            })
    
    print(f"  🥶 Found {len(cold_start_recipes)} cold start recipes")
    
    # Generate initial reviews for cold start recipes
    from malaysian_review_generator import (
        get_recipe_context, select_persona_for_recipe, 
        generate_review_content
    )
    
    # Get available users
    available_users = list(users_collection.find({}, {'_id': 1}))
    
    cold_start_reviews = []
    
    for recipe_data in cold_start_recipes[:50]:  # Limit to first 50 for performance
        recipe = recipe_data['recipe']
        recipe_context = get_recipe_context(recipe)
        
        # Generate 2-3 initial reviews per cold start recipe
        num_initial_reviews = random.randint(2, 3)
        selected_users = random.sample(available_users, min(num_initial_reviews, len(available_users)))
        
        for user in selected_users:
            user_id = str(user['_id'])
            persona = select_persona_for_recipe(recipe_context)
            
            # Cold start reviews tend to be slightly positive (3-4 stars)
            rating = random.choices([2, 3, 4, 5], weights=[0.1, 0.3, 0.4, 0.2])[0]
            
            review_text = generate_review_content(rating, recipe_context, persona)
            
            # Recent timestamps (within last 2 weeks)
            days_ago = random.randint(1, 14)
            created_at = datetime.now() - timedelta(days=days_ago)
            
            cold_start_review = {
                'recipe_id': recipe_data['recipe_id'],
                'user_id': user_id,
                'rating': rating,
                'review_text': review_text,
                'created_at': created_at,
                'updated_at': created_at,
                'helpful_count': random.randint(0, 1),
                'verified_purchase': True,
                'cuisine_context': recipe_context['cuisine'],
                'difficulty_rating': random.randint(max(1, rating-1), min(5, rating+1)),
                'is_cold_start_review': True
            }
            
            cold_start_reviews.append(cold_start_review)
    
    print(f"  ✅ Generated {len(cold_start_reviews)} cold start reviews")
    
    return cold_start_reviews

def implement_temporal_review_patterns(reviews):
    """Implement realistic temporal patterns for reviews."""
    print("\n⏰ Implementing temporal review patterns...")
    
    # Sort reviews by creation date
    reviews.sort(key=lambda x: x['created_at'])
    
    # Adjust timestamps to create realistic patterns
    current_time = datetime.now()
    
    for i, review in enumerate(reviews):
        # Create clusters of reviews (some recipes get reviewed in bursts)
        if random.random() < 0.3:  # 30% chance of burst pattern
            # Create a burst of reviews within a few days
            base_time = current_time - timedelta(days=random.randint(1, 60))
            review['created_at'] = base_time - timedelta(hours=random.randint(0, 72))
        else:
            # Spread reviews more evenly
            days_ago = random.randint(1, 90)
            review['created_at'] = current_time - timedelta(days=days_ago)
        
        review['updated_at'] = review['created_at']
    
    print(f"  ⏰ Applied temporal patterns to {len(reviews)} reviews")
    
    return reviews

def main():
    """Main cold start solving function."""
    print("🥶 SisaRasa Cold Start Problem Solver")
    print("=" * 40)
    print(f"Solving started at: {datetime.now().isoformat()}")
    
    # Connect to database
    db, client = connect_to_database()
    if db is None:
        return
    
    try:
        # Identify gateway recipes
        gateway_recipes, gateway_by_cuisine = identify_gateway_recipes(db)
        
        # Create onboarding strategy
        onboarding_archetypes = create_new_user_onboarding_strategy(gateway_recipes)
        
        # Solve new recipe cold start
        cold_start_reviews = solve_new_recipe_cold_start(db, gateway_by_cuisine)
        
        # Apply temporal patterns
        if cold_start_reviews:
            cold_start_reviews = implement_temporal_review_patterns(cold_start_reviews)
        
        # Insert cold start reviews
        if cold_start_reviews:
            reviews_collection = db['recipe_reviews']
            result = reviews_collection.insert_many(cold_start_reviews)
            print(f"  💾 Inserted {len(result.inserted_ids)} cold start reviews")
        
        # Save results
        results = {
            'solving_date': datetime.now().isoformat(),
            'gateway_recipes': len(gateway_recipes),
            'onboarding_archetypes': len(onboarding_archetypes),
            'cold_start_reviews_generated': len(cold_start_reviews),
            'gateway_recipes_by_cuisine': {k: len(v) for k, v in gateway_by_cuisine.items()},
            'archetype_details': [
                {
                    'name': arch['name'],
                    'description': arch['description'],
                    'preferred_cuisines': arch['preferred_cuisines']
                }
                for arch in onboarding_archetypes
            ]
        }
        
        with open('cold_start_solution.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Summary
        print(f"\n📋 COLD START SOLUTION SUMMARY")
        print("=" * 40)
        print(f"Gateway recipes identified: {len(gateway_recipes)}")
        print(f"Onboarding archetypes created: {len(onboarding_archetypes)}")
        print(f"Cold start reviews generated: {len(cold_start_reviews)}")
        print(f"Cuisines covered: {len(gateway_by_cuisine)}")
        print(f"Solution completed at: {datetime.now().isoformat()}")
        
    except Exception as e:
        print(f"❌ Error during cold start solving: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    main()
