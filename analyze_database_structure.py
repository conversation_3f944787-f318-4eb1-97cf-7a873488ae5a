#!/usr/bin/env python3
"""
Database Structure Analysis Script for SisaRasa Community Features

This script analyzes the current database structure, identifies issues,
and provides recommendations for cleanup and optimization.
"""

import os
import sys
from datetime import datetime
from pymongo import MongoClient
from bson import ObjectId
import json

# Add the project root to the path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        # Try to get MongoDB URI from environment or use default
        mongo_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client.get_default_database()
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None

def analyze_collection_structure(db, collection_name):
    """Analyze structure of a specific collection."""
    collection = db[collection_name]
    
    print(f"\n📊 Collection: {collection_name}")
    print("=" * 50)
    
    # Get document count
    doc_count = collection.count_documents({})
    print(f"Document count: {doc_count}")
    
    if doc_count == 0:
        print("Collection is empty.")
        return
    
    # Get sample documents
    sample_docs = list(collection.find().limit(3))
    
    # Analyze document structure
    if sample_docs:
        print("\nSample document structure:")
        first_doc = sample_docs[0]
        
        # Convert ObjectId to string for JSON serialization
        def convert_objectid(obj):
            if isinstance(obj, ObjectId):
                return str(obj)
            elif isinstance(obj, bytes):
                return f"<bytes: {len(obj)} bytes>"
            elif isinstance(obj, dict):
                return {k: convert_objectid(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_objectid(item) for item in obj]
            elif isinstance(obj, datetime):
                return obj.isoformat()
            return obj
        
        converted_doc = convert_objectid(first_doc)
        print(json.dumps(converted_doc, indent=2)[:500] + "..." if len(str(converted_doc)) > 500 else json.dumps(converted_doc, indent=2))
    
    # Get indexes
    indexes = list(collection.list_indexes())
    print(f"\nIndexes ({len(indexes)}):")
    for idx in indexes:
        print(f"  - {idx['name']}: {idx.get('key', {})}")
    
    return {
        'count': doc_count,
        'sample_docs': sample_docs,
        'indexes': indexes
    }

def analyze_community_posts(db):
    """Analyze community posts collection for issues."""
    print("\n🔍 Analyzing Community Posts")
    print("=" * 50)
    
    posts_collection = db['community_posts']
    
    # Check for posts without proper user references
    posts_without_users = list(posts_collection.find({'user_id': {'$exists': False}}))
    if posts_without_users:
        print(f"⚠️  Found {len(posts_without_users)} posts without user_id")
    
    # Check for posts with invalid user references
    all_posts = list(posts_collection.find())
    invalid_user_refs = []
    
    for post in all_posts:
        user_id = post.get('user_id')
        if user_id:
            user_exists = db.users.find_one({'_id': ObjectId(user_id) if isinstance(user_id, str) else user_id})
            if not user_exists:
                invalid_user_refs.append(post['_id'])
    
    if invalid_user_refs:
        print(f"⚠️  Found {len(invalid_user_refs)} posts with invalid user references")
    
    # Check for duplicate posts
    content_counts = {}
    for post in all_posts:
        content = post.get('content', '')
        if content in content_counts:
            content_counts[content] += 1
        else:
            content_counts[content] = 1
    
    duplicates = {k: v for k, v in content_counts.items() if v > 1}
    if duplicates:
        print(f"⚠️  Found {len(duplicates)} duplicate post contents")
    
    return {
        'posts_without_users': len(posts_without_users),
        'invalid_user_refs': len(invalid_user_refs),
        'duplicate_contents': len(duplicates)
    }

def analyze_shared_recipes(db):
    """Analyze shared recipes collection for issues."""
    print("\n🔍 Analyzing Shared Recipes")
    print("=" * 50)
    
    recipes_collection = db['recipes']
    
    # Find user-submitted recipes
    user_recipes = list(recipes_collection.find({'original_id': {'$regex': '^user_'}}))
    print(f"User-submitted recipes: {len(user_recipes)}")
    
    # Check for recipes without proper user references
    recipes_without_users = []
    for recipe in user_recipes:
        submitted_by = recipe.get('submitted_by')
        if not submitted_by:
            recipes_without_users.append(recipe['_id'])
        else:
            user_exists = db.users.find_one({'_id': ObjectId(submitted_by) if isinstance(submitted_by, str) else submitted_by})
            if not user_exists:
                recipes_without_users.append(recipe['_id'])
    
    if recipes_without_users:
        print(f"⚠️  Found {len(recipes_without_users)} recipes with invalid user references")
    
    return {
        'user_recipes_count': len(user_recipes),
        'invalid_user_refs': len(recipes_without_users)
    }

def check_api_consistency(db):
    """Check for API consistency issues."""
    print("\n🔍 Checking API Consistency")
    print("=" * 50)
    
    # Check if there are conflicting data structures
    posts = list(db.community_posts.find().limit(5))
    recipes = list(db.recipes.find({'original_id': {'$regex': '^user_'}}).limit(5))
    
    issues = []
    
    # Check post structure consistency
    if posts:
        required_post_fields = ['user_id', 'content', 'created_at']
        for post in posts:
            missing_fields = [field for field in required_post_fields if field not in post]
            if missing_fields:
                issues.append(f"Post {post['_id']} missing fields: {missing_fields}")
    
    # Check recipe structure consistency
    if recipes:
        required_recipe_fields = ['name', 'submitted_by', 'created_at']
        for recipe in recipes:
            missing_fields = [field for field in required_recipe_fields if field not in recipe]
            if missing_fields:
                issues.append(f"Recipe {recipe['_id']} missing fields: {missing_fields}")
    
    if issues:
        print("⚠️  Found API consistency issues:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ No major API consistency issues found")
    
    return issues

def main():
    """Main analysis function."""
    print("🔍 SisaRasa Database Structure Analysis")
    print("=" * 60)
    print(f"Analysis started at: {datetime.now().isoformat()}")
    
    # Connect to database
    db = connect_to_database()
    if db is None:
        return
    
    # List all collections
    collections = db.list_collection_names()
    print(f"\n📋 Found {len(collections)} collections:")
    for collection in collections:
        print(f"  - {collection}")
    
    # Analyze key collections
    key_collections = ['users', 'community_posts', 'post_comments', 'post_likes', 'recipes']
    analysis_results = {}
    
    for collection_name in key_collections:
        if collection_name in collections:
            analysis_results[collection_name] = analyze_collection_structure(db, collection_name)
        else:
            print(f"\n⚠️  Collection '{collection_name}' not found!")
    
    # Perform specific analyses
    community_issues = analyze_community_posts(db)
    recipe_issues = analyze_shared_recipes(db)
    api_issues = check_api_consistency(db)
    
    # Summary
    print("\n📋 ANALYSIS SUMMARY")
    print("=" * 60)
    print(f"Community Posts Issues:")
    print(f"  - Posts without users: {community_issues['posts_without_users']}")
    print(f"  - Invalid user references: {community_issues['invalid_user_refs']}")
    print(f"  - Duplicate contents: {community_issues['duplicate_contents']}")
    
    print(f"\nShared Recipes Issues:")
    print(f"  - User recipes count: {recipe_issues['user_recipes_count']}")
    print(f"  - Invalid user references: {recipe_issues['invalid_user_refs']}")
    
    print(f"\nAPI Issues: {len(api_issues)}")
    
    print(f"\n✅ Analysis completed at: {datetime.now().isoformat()}")

if __name__ == "__main__":
    main()
