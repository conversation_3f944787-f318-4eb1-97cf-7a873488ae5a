#!/usr/bin/env python3
"""
Script to find and delete the test recipe by <PERSON>.
"""

import os
import sys
from pymongo import MongoClient
from bson import ObjectId
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        # Get MongoDB URI from environment
        mongo_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017/')

        client = MongoClient(mongo_uri)
        db = client.get_default_database()

        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db, client
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None, None

def find_user_by_name(db, name):
    """Find user by name."""
    try:
        user = db.users.find_one({"name": {"$regex": name, "$options": "i"}})
        if user:
            print(f"Found user: {user['name']} (ID: {user['_id']}, Email: {user.get('email', 'N/A')})")
            return user
        else:
            print(f"No user found with name containing: {name}")
            return None
    except Exception as e:
        print(f"Error finding user: {e}")
        return None

def find_recipes_by_user_and_name(db, user_id, recipe_name_pattern):
    """Find recipes by user ID and recipe name pattern."""
    try:
        # Search for recipes submitted by the user with name matching pattern
        query = {
            "$and": [
                {"$or": [
                    {"submitted_by": str(user_id)},
                    {"submitter_id": str(user_id)}
                ]},
                {"name": {"$regex": recipe_name_pattern, "$options": "i"}},
                {"original_id": {"$regex": "^user_"}}  # Only user-submitted recipes
            ]
        }
        
        recipes = list(db.recipes.find(query))
        print(f"Found {len(recipes)} matching recipes:")
        
        for recipe in recipes:
            print(f"  - Recipe ID: {recipe['_id']}")
            print(f"    Name: {recipe.get('name', 'N/A')}")
            print(f"    Original ID: {recipe.get('original_id', 'N/A')}")
            print(f"    Submitted by: {recipe.get('submitted_by', recipe.get('submitter_id', 'N/A'))}")
            print(f"    Created: {recipe.get('created_at', 'N/A')}")
            print(f"    Cuisine: {recipe.get('cuisine', 'N/A')}")
            print()
        
        return recipes
    except Exception as e:
        print(f"Error finding recipes: {e}")
        return []

def delete_recipe_and_related_data(db, recipe_id):
    """Delete a recipe and all its related data."""
    try:
        recipe_id_str = str(recipe_id)
        
        print(f"Deleting recipe {recipe_id_str} and related data...")
        
        # Delete the recipe
        recipe_result = db.recipes.delete_one({'_id': ObjectId(recipe_id)})
        print(f"  Recipe deleted: {recipe_result.deleted_count} document(s)")
        
        # Delete related likes
        likes_result = db.recipe_likes.delete_many({'recipe_id': recipe_id_str})
        print(f"  Recipe likes deleted: {likes_result.deleted_count} document(s)")
        
        # Get comments to delete comment likes
        comments = list(db.recipe_comments.find({'recipe_id': recipe_id_str}))
        comment_likes_deleted = 0
        
        for comment in comments:
            comment_likes_result = db.recipe_comment_likes.delete_many({'comment_id': str(comment['_id'])})
            comment_likes_deleted += comment_likes_result.deleted_count
        
        print(f"  Comment likes deleted: {comment_likes_deleted} document(s)")
        
        # Delete comments
        comments_result = db.recipe_comments.delete_many({'recipe_id': recipe_id_str})
        print(f"  Recipe comments deleted: {comments_result.deleted_count} document(s)")
        
        # Delete any reviews (if they exist)
        reviews_result = db.recipe_reviews.delete_many({'recipe_id': recipe_id_str})
        print(f"  Recipe reviews deleted: {reviews_result.deleted_count} document(s)")
        
        print(f"✅ Successfully deleted recipe {recipe_id_str} and all related data")
        return True
        
    except Exception as e:
        print(f"❌ Error deleting recipe: {e}")
        return False

def main():
    """Main function."""
    print("🔍 Finding and deleting test recipe by Jamal Daud...")
    print("=" * 60)
    
    # Connect to database
    db, client = connect_to_database()
    if db is None:
        sys.exit(1)
    
    try:
        # Find user Jamal Daud
        user = find_user_by_name(db, "Jamal Daud")
        if not user:
            print("❌ User 'Jamal Daud' not found")
            return
        
        user_id = user['_id']
        
        # Find recipes with "Test Recipe" in the name
        recipes = find_recipes_by_user_and_name(db, user_id, "Test Recipe")
        
        if not recipes:
            print("❌ No test recipes found for Jamal Daud")
            return
        
        # Show recipes and ask for confirmation
        print(f"Found {len(recipes)} recipe(s) to delete:")
        for i, recipe in enumerate(recipes, 1):
            print(f"{i}. {recipe.get('name', 'N/A')} (ID: {recipe['_id']})")
        
        # Ask for confirmation
        response = input("\nDo you want to delete ALL these recipes? (yes/no): ").lower().strip()
        
        if response in ['yes', 'y']:
            deleted_count = 0
            for recipe in recipes:
                if delete_recipe_and_related_data(db, recipe['_id']):
                    deleted_count += 1
            
            print(f"\n✅ Successfully deleted {deleted_count} out of {len(recipes)} recipes")
        else:
            print("❌ Deletion cancelled")
    
    finally:
        client.close()
        print("🔌 Database connection closed")

if __name__ == "__main__":
    main()
