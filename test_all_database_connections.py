#!/usr/bin/env python3
"""
Comprehensive Database Connection Test
Tests all database connections to ensure they connect to MongoDB Atlas
"""

import os
import sys
import time
from datetime import datetime
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class DatabaseConnectionTester:
    def __init__(self):
        self.atlas_uri = os.getenv('MONGO_URI')
        self.test_results = {}
        
    def log(self, message, level="INFO"):
        """Log test steps."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def test_environment_variables(self):
        """Test environment variable configuration."""
        self.log("\n🔍 TESTING ENVIRONMENT VARIABLES")
        self.log("=" * 60)
        
        # Check MONGO_URI
        if self.atlas_uri:
            if 'mongodb+srv://' in self.atlas_uri and 'sisarasa.pzkt0dj.mongodb.net' in self.atlas_uri:
                self.log("✅ MONGO_URI correctly configured for Atlas")
                self.test_results['env_mongo_uri'] = True
            else:
                self.log("❌ MONGO_URI doesn't appear to be Atlas connection", "ERROR")
                self.test_results['env_mongo_uri'] = False
        else:
            self.log("❌ MONGO_URI environment variable not found", "ERROR")
            self.test_results['env_mongo_uri'] = False
            
        # Check MONGODB_URI (should not be used)
        mongodb_uri = os.getenv('MONGODB_URI')
        if mongodb_uri:
            self.log("⚠️ MONGODB_URI found - this should not be used", "WARNING")
            self.test_results['env_mongodb_uri_unused'] = False
        else:
            self.log("✅ MONGODB_URI not defined (correct)")
            self.test_results['env_mongodb_uri_unused'] = True
            
    def test_atlas_connection(self):
        """Test direct connection to Atlas."""
        self.log("\n🔗 TESTING ATLAS CONNECTION")
        self.log("=" * 60)
        
        if not self.atlas_uri:
            self.log("❌ Cannot test Atlas connection - MONGO_URI not found", "ERROR")
            self.test_results['atlas_connection'] = False
            return False
            
        try:
            client = MongoClient(self.atlas_uri, serverSelectionTimeoutMS=10000)
            db = client.get_default_database()
            
            # Test connection
            start_time = time.time()
            client.admin.command('ping')
            connection_time = (time.time() - start_time) * 1000
            
            self.log(f"✅ Successfully connected to Atlas")
            self.log(f"📊 Connection time: {connection_time:.2f}ms")
            self.log(f"🗄️ Database name: {db.name}")
            
            # Test basic operations
            collections = db.list_collection_names()
            self.log(f"📋 Collections found: {len(collections)}")
            
            if 'users' in collections:
                user_count = db.users.count_documents({})
                self.log(f"👥 Users in database: {user_count}")
                
            client.close()
            self.test_results['atlas_connection'] = True
            return True
            
        except Exception as e:
            self.log(f"❌ Atlas connection failed: {e}", "ERROR")
            self.test_results['atlas_connection'] = False
            return False
            
    def test_script_connections(self):
        """Test connections from various scripts."""
        self.log("\n🧪 TESTING SCRIPT CONNECTIONS")
        self.log("=" * 60)
        
        # Test scripts that should now use MONGO_URI
        test_scripts = [
            'quality_assurance_system.py',
            'final_verification_system.py',
            'database_optimization_cleanup.py',
            'malaysian_review_generator.py',
            'user_preference_optimizer.py',
            'recommendation_data_optimizer.py',
            'cold_start_solver.py',
            'database_cleanup.py',
            'clear_posts.py',
            'analyze_database_structure.py',
            'analyze_recipe_review_gaps.py'
        ]
        
        successful_scripts = 0
        
        for script in test_scripts:
            if os.path.exists(script):
                try:
                    # Read script content to verify it uses MONGO_URI
                    with open(script, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                    if "os.getenv('MONGO_URI'" in content:
                        self.log(f"✅ {script} - Uses MONGO_URI")
                        successful_scripts += 1
                    elif "os.getenv('MONGODB_URI'" in content:
                        self.log(f"❌ {script} - Still uses MONGODB_URI", "ERROR")
                    else:
                        self.log(f"⚠️ {script} - No clear environment variable usage", "WARNING")
                        
                except Exception as e:
                    self.log(f"❌ {script} - Error reading file: {e}", "ERROR")
            else:
                self.log(f"⚠️ {script} - File not found", "WARNING")
                
        self.log(f"📊 Scripts using MONGO_URI: {successful_scripts}/{len(test_scripts)}")
        self.test_results['script_connections'] = successful_scripts == len([s for s in test_scripts if os.path.exists(s)])
        
    def test_hardcoded_credentials(self):
        """Test for hardcoded credentials."""
        self.log("\n🔒 TESTING FOR HARDCODED CREDENTIALS")
        self.log("=" * 60)
        
        # Scripts that previously had hardcoded credentials
        security_scripts = [
            'mongodb_test_connection.py',
            'python_atlas_migration.py',
            'delete_test_recipe.py'
        ]
        
        secure_scripts = 0
        
        for script in security_scripts:
            if os.path.exists(script):
                try:
                    with open(script, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                    # Check for hardcoded Atlas URI
                    if 'mongodb+srv://farahfiqh:' in content:
                        self.log(f"❌ {script} - Contains hardcoded credentials", "ERROR")
                    else:
                        self.log(f"✅ {script} - No hardcoded credentials found")
                        secure_scripts += 1
                        
                except Exception as e:
                    self.log(f"❌ {script} - Error reading file: {e}", "ERROR")
            else:
                self.log(f"⚠️ {script} - File not found", "WARNING")
                
        self.log(f"📊 Secure scripts: {secure_scripts}/{len(security_scripts)}")
        self.test_results['security_check'] = secure_scripts == len([s for s in security_scripts if os.path.exists(s)])
        
    def generate_report(self):
        """Generate final test report."""
        self.log("\n📋 FINAL TEST REPORT")
        self.log("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        self.log(f"📊 Tests passed: {passed_tests}/{total_tests}")
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            self.log(f"{status} - {test_name}")
            
        if passed_tests == total_tests:
            self.log("\n🎉 ALL TESTS PASSED - Database connections are properly configured!")
            return True
        else:
            self.log(f"\n⚠️ {total_tests - passed_tests} tests failed - Review issues above")
            return False
            
    def run_all_tests(self):
        """Run all database connection tests."""
        self.log("🚀 Starting comprehensive database connection test...")
        
        self.test_environment_variables()
        self.test_atlas_connection()
        self.test_script_connections()
        self.test_hardcoded_credentials()
        
        return self.generate_report()

def main():
    """Main function."""
    tester = DatabaseConnectionTester()
    success = tester.run_all_tests()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
