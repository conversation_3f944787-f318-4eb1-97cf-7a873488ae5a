#!/usr/bin/env python3
"""
User Preference Pattern Optimizer

This script optimizes user preference patterns and creates bridge users
to improve collaborative filtering connectivity for the recommendation engine.
"""

import os
import sys
import json
import random
from datetime import datetime, timedelta
from pymongo import MongoClient
from bson import ObjectId
from collections import defaultdict, Counter

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        mongo_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client.get_default_database()
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db, client
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None, None

def analyze_current_user_patterns(db):
    """Analyze current user review patterns."""
    print("\n📊 Analyzing current user patterns...")
    
    reviews_collection = db['recipe_reviews']
    recipes_collection = db['recipes']
    
    # Get user review patterns
    user_patterns = defaultdict(lambda: {
        'reviews': [],
        'cuisines': set(),
        'avg_rating': 0,
        'rating_variance': 0,
        'review_frequency': 0
    })
    
    # Analyze all reviews
    for review in reviews_collection.find():
        user_id = review.get('user_id')
        recipe_id = review.get('recipe_id')
        rating = review.get('rating', 0)
        created_at = review.get('created_at', datetime.now())
        
        if user_id and recipe_id:
            # Get recipe cuisine
            try:
                recipe = recipes_collection.find_one({'_id': ObjectId(recipe_id)})
                cuisine = recipe.get('cuisine', 'Unknown') if recipe else 'Unknown'
            except:
                cuisine = 'Unknown'
            
            user_patterns[user_id]['reviews'].append({
                'recipe_id': recipe_id,
                'rating': rating,
                'cuisine': cuisine,
                'created_at': created_at
            })
            user_patterns[user_id]['cuisines'].add(cuisine)
    
    # Calculate metrics for each user
    for user_id, pattern in user_patterns.items():
        reviews = pattern['reviews']
        if reviews:
            ratings = [r['rating'] for r in reviews]
            pattern['avg_rating'] = sum(ratings) / len(ratings)
            pattern['rating_variance'] = sum((r - pattern['avg_rating'])**2 for r in ratings) / len(ratings)
            
            # Calculate review frequency (reviews per month)
            if len(reviews) > 1:
                date_range = max(r['created_at'] for r in reviews) - min(r['created_at'] for r in reviews)
                months = max(1, date_range.days / 30)
                pattern['review_frequency'] = len(reviews) / months
            else:
                pattern['review_frequency'] = 1
    
    print(f"  📈 Analyzed {len(user_patterns)} users with reviews")
    
    return dict(user_patterns)

def identify_bridge_users(user_patterns):
    """Identify existing bridge users and gaps."""
    print("\n🌉 Identifying bridge users...")
    
    bridge_users = []
    cuisine_coverage = defaultdict(set)  # cuisine -> set of users who review it
    
    for user_id, pattern in user_patterns.items():
        cuisine_count = len(pattern['cuisines'])
        
        # Bridge users review 3+ different cuisines
        if cuisine_count >= 3:
            bridge_users.append({
                'user_id': user_id,
                'cuisine_count': cuisine_count,
                'cuisines': list(pattern['cuisines']),
                'review_count': len(pattern['reviews']),
                'avg_rating': pattern['avg_rating']
            })
        
        # Track cuisine coverage
        for cuisine in pattern['cuisines']:
            cuisine_coverage[cuisine].add(user_id)
    
    print(f"  🌉 Found {len(bridge_users)} existing bridge users")
    
    # Identify cuisine connectivity gaps
    cuisine_pairs = {}
    cuisines = list(cuisine_coverage.keys())
    
    for i, cuisine1 in enumerate(cuisines):
        for cuisine2 in cuisines[i+1:]:
            # Users who review both cuisines
            common_users = cuisine_coverage[cuisine1] & cuisine_coverage[cuisine2]
            cuisine_pairs[(cuisine1, cuisine2)] = len(common_users)
    
    # Find poorly connected cuisine pairs
    weak_connections = [(pair, count) for pair, count in cuisine_pairs.items() if count < 3]
    weak_connections.sort(key=lambda x: x[1])
    
    print(f"  ⚠️  Found {len(weak_connections)} weakly connected cuisine pairs")
    
    return bridge_users, weak_connections, cuisine_coverage

def create_bridge_user_strategy(db, user_patterns, weak_connections, cuisine_coverage):
    """Create strategy to improve user connectivity."""
    print("\n📋 Creating bridge user strategy...")
    
    users_collection = db['users']
    all_users = list(users_collection.find({}, {'_id': 1}))
    
    # Find users with few reviews who can become bridge users
    potential_bridge_users = []
    
    for user in all_users:
        user_id = str(user['_id'])
        pattern = user_patterns.get(user_id, {'reviews': [], 'cuisines': set()})
        
        review_count = len(pattern['reviews'])
        cuisine_count = len(pattern['cuisines'])
        
        # Users with 0-2 reviews are good candidates for bridge expansion
        if review_count <= 2:
            potential_bridge_users.append({
                'user_id': user_id,
                'current_reviews': review_count,
                'current_cuisines': list(pattern['cuisines']),
                'potential_score': random.random()  # Random selection for diversity
            })
    
    # Sort by potential score
    potential_bridge_users.sort(key=lambda x: x['potential_score'], reverse=True)
    
    # Create bridge assignments
    bridge_assignments = []
    
    # Target: Create bridges for the weakest connections first
    for (cuisine1, cuisine2), connection_count in weak_connections[:20]:  # Top 20 weakest
        if connection_count < 5:  # Need more bridges
            needed_bridges = 5 - connection_count
            
            # Find users to bridge these cuisines
            for _ in range(min(needed_bridges, len(potential_bridge_users))):
                if potential_bridge_users:
                    user = potential_bridge_users.pop(0)
                    bridge_assignments.append({
                        'user_id': user['user_id'],
                        'target_cuisines': [cuisine1, cuisine2],
                        'reviews_to_add': random.randint(2, 4)
                    })
    
    print(f"  🎯 Created {len(bridge_assignments)} bridge user assignments")
    
    return bridge_assignments

def generate_bridge_reviews(db, bridge_assignments):
    """Generate reviews for bridge users."""
    print("\n📝 Generating bridge user reviews...")
    
    recipes_collection = db['recipes']
    
    # Import review generation functions
    from malaysian_review_generator import (
        get_recipe_context, select_persona_for_recipe, 
        generate_review_content, MALAYSIAN_PERSONAS
    )
    
    all_bridge_reviews = []
    
    for assignment in bridge_assignments:
        user_id = assignment['user_id']
        target_cuisines = assignment['target_cuisines']
        reviews_to_add = assignment['reviews_to_add']
        
        # Get recipes from target cuisines
        available_recipes = []
        for cuisine in target_cuisines:
            recipes = list(recipes_collection.find({'cuisine': cuisine}).limit(10))
            available_recipes.extend(recipes)
        
        if len(available_recipes) < reviews_to_add:
            # Get additional recipes from related cuisines
            additional_recipes = list(recipes_collection.find({
                'cuisine': {'$nin': target_cuisines}
            }).limit(reviews_to_add - len(available_recipes)))
            available_recipes.extend(additional_recipes)
        
        # Select recipes for this user
        selected_recipes = random.sample(
            available_recipes, 
            min(reviews_to_add, len(available_recipes))
        )
        
        # Generate reviews
        for recipe in selected_recipes:
            recipe_context = get_recipe_context(recipe)
            persona = select_persona_for_recipe(recipe_context)
            
            # Bridge users tend to give balanced ratings (3-4 stars mostly)
            rating = random.choices([2, 3, 4, 5], weights=[0.1, 0.4, 0.4, 0.1])[0]
            
            review_text = generate_review_content(rating, recipe_context, persona)
            
            # Generate timestamp
            days_ago = random.randint(1, 45)
            created_at = datetime.now() - timedelta(days=days_ago)
            
            bridge_review = {
                'recipe_id': str(recipe['_id']),
                'user_id': user_id,
                'rating': rating,
                'review_text': review_text,
                'created_at': created_at,
                'updated_at': created_at,
                'helpful_count': random.randint(0, 3),
                'verified_purchase': True,  # Bridge users are "verified"
                'cuisine_context': recipe_context['cuisine'],
                'difficulty_rating': random.randint(max(1, rating-1), min(5, rating+1)),
                'is_bridge_review': True  # Mark for tracking
            }
            
            all_bridge_reviews.append(bridge_review)
    
    print(f"  ✅ Generated {len(all_bridge_reviews)} bridge reviews")
    return all_bridge_reviews

def create_user_preference_clusters(db, user_patterns):
    """Create user preference clusters for better collaborative filtering."""
    print("\n👥 Creating user preference clusters...")
    
    # Define preference clusters based on rating patterns and cuisine preferences
    clusters = {
        'spicy_enthusiasts': {
            'cuisines': ['Malay', 'Indian', 'Thai'],
            'rating_bias': 0.5,  # Tend to rate spicy food higher
            'users': []
        },
        'traditional_lovers': {
            'cuisines': ['Malay', 'Chinese', 'Indian', 'Peranakan'],
            'rating_bias': 0.3,
            'users': []
        },
        'international_explorers': {
            'cuisines': ['International', 'Western', 'Fusion'],
            'rating_bias': 0.2,
            'users': []
        },
        'health_conscious': {
            'cuisines': ['International', 'Chinese'],
            'rating_bias': -0.2,  # More critical of unhealthy food
            'users': []
        },
        'comfort_food_seekers': {
            'cuisines': ['Malay', 'Chinese', 'International'],
            'rating_bias': 0.4,
            'users': []
        }
    }
    
    # Assign users to clusters based on their review patterns
    for user_id, pattern in user_patterns.items():
        user_cuisines = pattern['cuisines']
        avg_rating = pattern['avg_rating']
        
        # Calculate cluster scores
        cluster_scores = {}
        for cluster_name, cluster_info in clusters.items():
            # Score based on cuisine overlap
            cuisine_overlap = len(set(cluster_info['cuisines']) & user_cuisines)
            cuisine_score = cuisine_overlap / len(cluster_info['cuisines'])
            
            # Score based on rating pattern
            expected_rating = 3.5 + cluster_info['rating_bias']
            rating_score = 1 - abs(avg_rating - expected_rating) / 2
            
            cluster_scores[cluster_name] = (cuisine_score * 0.7) + (rating_score * 0.3)
        
        # Assign to best matching cluster
        best_cluster = max(cluster_scores.items(), key=lambda x: x[1])[0]
        clusters[best_cluster]['users'].append({
            'user_id': user_id,
            'score': cluster_scores[best_cluster],
            'review_count': len(pattern['reviews'])
        })
    
    # Print cluster summary
    for cluster_name, cluster_info in clusters.items():
        print(f"  🏷️  {cluster_name}: {len(cluster_info['users'])} users")
    
    return clusters

def save_optimization_results(bridge_assignments, clusters, bridge_reviews):
    """Save optimization results for analysis."""
    results = {
        'optimization_date': datetime.now().isoformat(),
        'bridge_assignments': len(bridge_assignments),
        'bridge_reviews_generated': len(bridge_reviews),
        'user_clusters': {name: len(info['users']) for name, info in clusters.items()},
        'bridge_assignments_detail': bridge_assignments[:10],  # Sample
        'cluster_summary': {
            name: {
                'user_count': len(info['users']),
                'cuisines': info['cuisines'],
                'rating_bias': info['rating_bias']
            }
            for name, info in clusters.items()
        }
    }
    
    with open('user_preference_optimization.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Optimization results saved to: user_preference_optimization.json")

def main():
    """Main optimization function."""
    print("👥 SisaRasa User Preference Pattern Optimization")
    print("=" * 50)
    print(f"Optimization started at: {datetime.now().isoformat()}")
    
    # Connect to database
    db, client = connect_to_database()
    if db is None:
        return
    
    try:
        # Analyze current patterns
        user_patterns = analyze_current_user_patterns(db)
        
        # Identify bridge users and gaps
        bridge_users, weak_connections, cuisine_coverage = identify_bridge_users(user_patterns)
        
        # Create bridge user strategy
        bridge_assignments = create_bridge_user_strategy(db, user_patterns, weak_connections, cuisine_coverage)
        
        # Generate bridge reviews
        bridge_reviews = generate_bridge_reviews(db, bridge_assignments)
        
        # Create user preference clusters
        clusters = create_user_preference_clusters(db, user_patterns)
        
        # Insert bridge reviews
        if bridge_reviews:
            reviews_collection = db['recipe_reviews']
            result = reviews_collection.insert_many(bridge_reviews)
            print(f"  💾 Inserted {len(result.inserted_ids)} bridge reviews")
        
        # Save results
        save_optimization_results(bridge_assignments, clusters, bridge_reviews)
        
        # Summary
        print(f"\n📋 USER PREFERENCE OPTIMIZATION SUMMARY")
        print("=" * 50)
        print(f"Existing bridge users: {len(bridge_users)}")
        print(f"New bridge assignments: {len(bridge_assignments)}")
        print(f"Bridge reviews generated: {len(bridge_reviews)}")
        print(f"User clusters created: {len(clusters)}")
        print(f"Weak connections addressed: {min(20, len(weak_connections))}")
        print(f"Optimization completed at: {datetime.now().isoformat()}")
        
    except Exception as e:
        print(f"❌ Error during optimization: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    main()
