
import os
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get Atlas connection string from environment
atlas_uri = os.getenv('MONGO_URI')
if not atlas_uri:
    print("❌ MONGO_URI environment variable not found")
    exit(1)

try:
    client = MongoClient(atlas_uri)
    # Test connection
    client.admin.command('ping')
    print("✅ Successfully connected to Atlas!")
    
    # List databases
    databases = client.list_database_names()
    print(f"Available databases: {databases}")
    
except Exception as e:
    print(f"❌ Connection failed: {e}")
finally:
    client.close()