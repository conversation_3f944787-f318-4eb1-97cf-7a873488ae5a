#!/usr/bin/env python3
"""
Quality Assurance System for Generated Data

This script validates and ensures the quality of generated reviews and data
to maintain natural, human-like patterns and cultural appropriateness.
"""

import os
import sys
import json
import re
import random
from datetime import datetime, timedelta
from pymongo import MongoClient
from bson import ObjectId
from collections import defaultdict, Counter
import statistics

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        mongo_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client.get_default_database()
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db, client
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None, None

def analyze_review_naturalness(db):
    """Analyze reviews for natural, human-like patterns."""
    print("\n🔍 Analyzing review naturalness...")
    
    reviews_collection = db['recipe_reviews']
    
    # Get all reviews
    all_reviews = list(reviews_collection.find())
    
    naturalness_metrics = {
        'total_reviews': len(all_reviews),
        'length_distribution': [],
        'sentence_variety': [],
        'repetition_issues': [],
        'language_patterns': {
            'malaysian_terms': 0,
            'english_only': 0,
            'mixed_language': 0
        },
        'rating_distribution': defaultdict(int),
        'temporal_patterns': [],
        'user_behavior_patterns': defaultdict(list)
    }
    
    # Malaysian terms to check for
    malaysian_terms = [
        'sedap', 'best', 'power', 'shiok', 'mantap', 'terbaik',
        'pedas', 'lemak', 'kurang', 'tak', 'lah', 'je', 'sangat',
        'memang', 'confirm', 'syok', 'hambar'
    ]
    
    for review in all_reviews:
        review_text = review.get('review_text', '') or ''  # Handle None values
        rating = review.get('rating', 0)
        user_id = review.get('user_id', '') or ''
        created_at = review.get('created_at', datetime.now())

        # Skip empty reviews
        if not review_text.strip():
            continue

        # Length analysis
        word_count = len(review_text.split())
        naturalness_metrics['length_distribution'].append(word_count)
        
        # Sentence variety
        sentences = review_text.split('.')
        sentence_count = len([s for s in sentences if s.strip()])
        naturalness_metrics['sentence_variety'].append(sentence_count)
        
        # Language pattern analysis
        has_malaysian = any(term in review_text.lower() for term in malaysian_terms)
        if has_malaysian:
            naturalness_metrics['language_patterns']['malaysian_terms'] += 1
        else:
            naturalness_metrics['language_patterns']['english_only'] += 1
        
        # Rating distribution
        naturalness_metrics['rating_distribution'][rating] += 1
        
        # User behavior patterns
        naturalness_metrics['user_behavior_patterns'][user_id].append({
            'rating': rating,
            'length': word_count,
            'created_at': created_at
        })
        
        # Temporal patterns
        naturalness_metrics['temporal_patterns'].append(created_at)
    
    # Calculate statistics
    lengths = naturalness_metrics['length_distribution']
    if lengths:
        naturalness_metrics['length_stats'] = {
            'mean': statistics.mean(lengths),
            'median': statistics.median(lengths),
            'std_dev': statistics.stdev(lengths) if len(lengths) > 1 else 0,
            'min': min(lengths),
            'max': max(lengths)
        }
    
    # Check for repetition issues
    review_texts = [r.get('review_text', '') for r in all_reviews]
    text_counts = Counter(review_texts)
    duplicates = {text: count for text, count in text_counts.items() if count > 1 and text.strip()}
    naturalness_metrics['repetition_issues'] = len(duplicates)
    
    print(f"  📊 Analyzed {len(all_reviews)} reviews")
    print(f"  📝 Average review length: {naturalness_metrics['length_stats']['mean']:.1f} words")
    print(f"  🇲🇾 Malaysian terms usage: {naturalness_metrics['language_patterns']['malaysian_terms']} reviews")
    print(f"  🔄 Duplicate reviews found: {naturalness_metrics['repetition_issues']}")
    
    return naturalness_metrics

def validate_cultural_appropriateness(db):
    """Validate cultural appropriateness of generated content."""
    print("\n🇲🇾 Validating cultural appropriateness...")
    
    reviews_collection = db['recipe_reviews']
    recipes_collection = db['recipes']
    
    cultural_validation = {
        'cuisine_review_alignment': {},
        'inappropriate_content': [],
        'cultural_term_usage': {},
        'rating_cultural_consistency': {}
    }
    
    # Define culturally appropriate terms by cuisine
    cultural_terms = {
        'Malay': ['sedap', 'pedas', 'lemak', 'rasa', 'mudah', 'senang', 'best', 'mantap'],
        'Chinese': ['nice', 'good', 'not bad', 'quite', 'can try', 'overall'],
        'Indian': ['superb', 'excellent', 'fantastic', 'spicy', 'healthy', 'nutritious'],
        'International': ['delicious', 'tasty', 'great', 'amazing', 'wonderful']
    }
    
    # Check reviews against recipes
    for review in reviews_collection.find():
        recipe_id = review.get('recipe_id')
        review_text = review.get('review_text', '').lower()
        rating = review.get('rating', 0)
        
        if recipe_id:
            try:
                recipe = recipes_collection.find_one({'_id': ObjectId(recipe_id)})
                if recipe:
                    cuisine = recipe.get('cuisine', 'Unknown')
                    
                    # Check cultural term alignment
                    if cuisine in cultural_terms:
                        cuisine_terms = cultural_terms[cuisine]
                        used_terms = [term for term in cuisine_terms if term in review_text]
                        
                        if cuisine not in cultural_validation['cultural_term_usage']:
                            cultural_validation['cultural_term_usage'][cuisine] = []
                        
                        cultural_validation['cultural_term_usage'][cuisine].extend(used_terms)
                    
                    # Check for inappropriate content
                    inappropriate_patterns = [
                        r'fake', r'artificial', r'generated', r'bot', r'automated',
                        r'script', r'template', r'copy', r'paste'
                    ]
                    
                    for pattern in inappropriate_patterns:
                        if re.search(pattern, review_text):
                            cultural_validation['inappropriate_content'].append({
                                'review_id': str(review['_id']),
                                'pattern': pattern,
                                'text_snippet': review_text[:100]
                            })
            except:
                continue
    
    # Summarize cultural term usage
    for cuisine, terms in cultural_validation['cultural_term_usage'].items():
        term_counts = Counter(terms)
        cultural_validation['cultural_term_usage'][cuisine] = dict(term_counts)
    
    print(f"  ✅ Cultural validation completed")
    print(f"  🚫 Inappropriate content instances: {len(cultural_validation['inappropriate_content'])}")
    
    return cultural_validation

def check_rating_distribution_realism(db):
    """Check if rating distributions are realistic."""
    print("\n⭐ Checking rating distribution realism...")
    
    reviews_collection = db['recipe_reviews']
    recipes_collection = db['recipes']
    
    # Analyze rating patterns
    recipe_ratings = defaultdict(list)
    overall_ratings = []
    
    for review in reviews_collection.find():
        recipe_id = review.get('recipe_id')
        rating = review.get('rating', 0)
        
        if recipe_id and rating:
            recipe_ratings[recipe_id].append(rating)
            overall_ratings.append(rating)
    
    # Calculate statistics
    rating_stats = {
        'total_reviews': len(overall_ratings),
        'overall_distribution': dict(Counter(overall_ratings)),
        'average_rating': statistics.mean(overall_ratings) if overall_ratings else 0,
        'rating_variance': statistics.variance(overall_ratings) if len(overall_ratings) > 1 else 0,
        'recipes_with_ratings': len(recipe_ratings),
        'recipe_rating_stats': {}
    }
    
    # Analyze per-recipe statistics
    recipe_averages = []
    for recipe_id, ratings in recipe_ratings.items():
        if len(ratings) >= 2:  # Only analyze recipes with multiple ratings
            avg_rating = statistics.mean(ratings)
            rating_variance = statistics.variance(ratings)
            recipe_averages.append(avg_rating)
            
            rating_stats['recipe_rating_stats'][recipe_id] = {
                'count': len(ratings),
                'average': avg_rating,
                'variance': rating_variance,
                'distribution': dict(Counter(ratings))
            }
    
    # Check for unrealistic patterns
    realism_issues = []
    
    # Check for too many perfect 5-star ratings
    five_star_percentage = (rating_stats['overall_distribution'].get(5, 0) / 
                           rating_stats['total_reviews'] * 100)
    if five_star_percentage > 40:  # More than 40% is unrealistic
        realism_issues.append(f"Too many 5-star ratings: {five_star_percentage:.1f}%")
    
    # Check for too few low ratings
    low_rating_percentage = ((rating_stats['overall_distribution'].get(1, 0) + 
                             rating_stats['overall_distribution'].get(2, 0)) / 
                            rating_stats['total_reviews'] * 100)
    if low_rating_percentage < 5:  # Less than 5% is unrealistic
        realism_issues.append(f"Too few low ratings: {low_rating_percentage:.1f}%")
    
    # Check average rating realism
    if rating_stats['average_rating'] > 4.5:
        realism_issues.append(f"Overall average too high: {rating_stats['average_rating']:.2f}")
    
    rating_stats['realism_issues'] = realism_issues
    
    print(f"  📊 Overall average rating: {rating_stats['average_rating']:.2f}")
    print(f"  ⭐ Rating distribution: {rating_stats['overall_distribution']}")
    print(f"  ⚠️  Realism issues found: {len(realism_issues)}")
    
    return rating_stats

def validate_temporal_patterns(db):
    """Validate temporal patterns in reviews."""
    print("\n⏰ Validating temporal patterns...")
    
    reviews_collection = db['recipe_reviews']
    
    # Get review timestamps
    review_timestamps = []
    user_review_patterns = defaultdict(list)
    
    for review in reviews_collection.find():
        created_at = review.get('created_at')
        user_id = review.get('user_id')
        
        if created_at and user_id:
            review_timestamps.append(created_at)
            user_review_patterns[user_id].append(created_at)
    
    temporal_validation = {
        'total_reviews_with_timestamps': len(review_timestamps),
        'date_range': {},
        'temporal_distribution': {},
        'user_temporal_patterns': {},
        'suspicious_patterns': []
    }
    
    if review_timestamps:
        # Date range analysis
        min_date = min(review_timestamps)
        max_date = max(review_timestamps)
        temporal_validation['date_range'] = {
            'earliest': min_date.isoformat(),
            'latest': max_date.isoformat(),
            'span_days': (max_date - min_date).days
        }
        
        # Check for suspicious patterns
        # 1. Too many reviews on same day
        date_counts = Counter(ts.date() for ts in review_timestamps)
        max_daily_reviews = max(date_counts.values())
        if max_daily_reviews > 50:  # More than 50 reviews per day is suspicious
            temporal_validation['suspicious_patterns'].append(
                f"Too many reviews on single day: {max_daily_reviews}"
            )
        
        # 2. Check user review frequency
        for user_id, timestamps in user_review_patterns.items():
            if len(timestamps) > 1:
                timestamps.sort()
                intervals = [(timestamps[i+1] - timestamps[i]).total_seconds() / 3600 
                           for i in range(len(timestamps)-1)]  # Hours between reviews
                
                avg_interval = statistics.mean(intervals)
                min_interval = min(intervals)
                
                # Flag users who review too frequently
                if min_interval < 1:  # Less than 1 hour between reviews
                    temporal_validation['suspicious_patterns'].append(
                        f"User {user_id} reviews too frequently: {min_interval:.1f} hours"
                    )
    
    print(f"  📅 Date range: {temporal_validation['date_range'].get('span_days', 0)} days")
    print(f"  ⚠️  Suspicious patterns: {len(temporal_validation['suspicious_patterns'])}")
    
    return temporal_validation

def generate_quality_report(naturalness_metrics, cultural_validation, rating_stats, temporal_validation):
    """Generate comprehensive quality assurance report."""
    print("\n📋 Generating quality assurance report...")
    
    # Calculate overall quality score
    quality_score = 100
    issues = []
    
    # Deduct points for issues
    if naturalness_metrics['repetition_issues'] > 10:
        quality_score -= 15
        issues.append(f"High repetition: {naturalness_metrics['repetition_issues']} duplicates")
    
    if len(cultural_validation['inappropriate_content']) > 0:
        quality_score -= 20
        issues.append(f"Inappropriate content: {len(cultural_validation['inappropriate_content'])} instances")
    
    if len(rating_stats['realism_issues']) > 0:
        quality_score -= 10
        issues.append(f"Rating realism issues: {len(rating_stats['realism_issues'])}")
    
    if len(temporal_validation['suspicious_patterns']) > 0:
        quality_score -= 15
        issues.append(f"Temporal issues: {len(temporal_validation['suspicious_patterns'])}")
    
    # Check Malaysian term usage
    malaysian_percentage = (naturalness_metrics['language_patterns']['malaysian_terms'] / 
                           naturalness_metrics['total_reviews'] * 100)
    if malaysian_percentage < 30:  # Less than 30% is concerning
        quality_score -= 10
        issues.append(f"Low Malaysian term usage: {malaysian_percentage:.1f}%")
    
    quality_report = {
        'report_date': datetime.now().isoformat(),
        'overall_quality_score': max(0, quality_score),
        'quality_grade': get_quality_grade(quality_score),
        'total_reviews_analyzed': naturalness_metrics['total_reviews'],
        'issues_found': issues,
        'detailed_metrics': {
            'naturalness': naturalness_metrics,
            'cultural_appropriateness': cultural_validation,
            'rating_realism': rating_stats,
            'temporal_patterns': temporal_validation
        },
        'recommendations': generate_recommendations(issues, naturalness_metrics, cultural_validation)
    }
    
    return quality_report

def get_quality_grade(score):
    """Convert quality score to letter grade."""
    if score >= 90:
        return 'A'
    elif score >= 80:
        return 'B'
    elif score >= 70:
        return 'C'
    elif score >= 60:
        return 'D'
    else:
        return 'F'

def generate_recommendations(issues, naturalness_metrics, cultural_validation):
    """Generate recommendations based on quality analysis."""
    recommendations = []
    
    if naturalness_metrics['repetition_issues'] > 10:
        recommendations.append("Increase review template variety to reduce repetition")
    
    if len(cultural_validation['inappropriate_content']) > 0:
        recommendations.append("Review and filter inappropriate content patterns")
    
    malaysian_percentage = (naturalness_metrics['language_patterns']['malaysian_terms'] / 
                           naturalness_metrics['total_reviews'] * 100)
    if malaysian_percentage < 30:
        recommendations.append("Increase usage of Malaysian English terms and expressions")
    
    if naturalness_metrics['length_stats']['std_dev'] < 5:
        recommendations.append("Increase variety in review lengths for more natural patterns")
    
    return recommendations

def main():
    """Main quality assurance function."""
    print("🔍 SisaRasa Quality Assurance System")
    print("=" * 40)
    print(f"Quality check started at: {datetime.now().isoformat()}")
    
    # Connect to database
    db, client = connect_to_database()
    if db is None:
        return
    
    try:
        # Run all quality checks
        naturalness_metrics = analyze_review_naturalness(db)
        cultural_validation = validate_cultural_appropriateness(db)
        rating_stats = check_rating_distribution_realism(db)
        temporal_validation = validate_temporal_patterns(db)
        
        # Generate comprehensive report
        quality_report = generate_quality_report(
            naturalness_metrics, cultural_validation, rating_stats, temporal_validation
        )
        
        # Save report
        with open('quality_assurance_report.json', 'w') as f:
            json.dump(quality_report, f, indent=2, default=str)
        
        # Print summary
        print(f"\n📋 QUALITY ASSURANCE SUMMARY")
        print("=" * 40)
        print(f"Overall Quality Score: {quality_report['overall_quality_score']}/100 (Grade: {quality_report['quality_grade']})")
        print(f"Total Reviews Analyzed: {quality_report['total_reviews_analyzed']}")
        print(f"Issues Found: {len(quality_report['issues_found'])}")
        
        if quality_report['issues_found']:
            print("\n⚠️  Issues Identified:")
            for issue in quality_report['issues_found']:
                print(f"  • {issue}")
        
        if quality_report['recommendations']:
            print("\n💡 Recommendations:")
            for rec in quality_report['recommendations']:
                print(f"  • {rec}")
        
        print(f"\n📄 Detailed report saved to: quality_assurance_report.json")
        print(f"Quality check completed at: {datetime.now().isoformat()}")
        
    except Exception as e:
        print(f"❌ Error during quality assurance: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    main()
