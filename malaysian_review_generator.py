#!/usr/bin/env python3
"""
Malaysian Culturally Authentic Review Generator

This script generates authentic Malaysian reviews using local English patterns,
food terminology, and multicultural personas for the SisaRasa recommendation system.
"""

import os
import random
import json
from datetime import datetime, timedelta
from pymongo import MongoClient
from bson import ObjectId

# Malaysian English patterns and terminology
MALAYSIAN_FOOD_TERMS = {
    'taste_positive': [
        'sedap', 'sedap gila', 'best', 'power', 'shiok', 'mantap', 'terbaik',
        'sedap macam mak masak', 'confirm sedap', 'memang best', 'syok sendiri'
    ],
    'taste_negative': [
        'kurang rasa', 'tak sedap', 'hambar', 'tak best', 'biasa je',
        'kurang kick', 'tak syok', 'disappointing lah'
    ],
    'spice_levels': [
        'pedas gila', 'pedas sikit', 'tak pedas', 'just nice pedas',
        'pedas macam nak mati', 'mild pedas', 'cukup pedas', 'pedas sederhana'
    ],
    'texture_terms': [
        'lembut', 'gebu', 'crispy', 'crunchy', 'tender', 'juicy',
        'creamy', 'lemak', 'smooth', 'fluffy', 'chewy'
    ],
    'cooking_praise': [
        'mudah nak buat', 'senang je', 'simple tapi sedap', 'tak susah',
        'boleh try ni', 'mesti try', 'worth it', 'recommended'
    ],
    'family_context': [
        'anak-anak suka', 'suami puji', 'family approve', 'mak saya suka',
        'kids love it', 'husband cakap sedap', 'whole family enjoy'
    ]
}

# Malaysian personas with cultural backgrounds
MALAYSIAN_PERSONAS = [
    {
        'name_pattern': 'malay_female',
        'language_style': 'casual_malay_english',
        'food_preferences': ['spicy', 'traditional', 'rice_based'],
        'review_style': 'family_oriented',
        'common_phrases': ['Alhamdulillah', 'InsyaAllah', 'sedap sangat', 'memang best']
    },
    {
        'name_pattern': 'chinese_female',
        'language_style': 'chinese_malaysian_english',
        'food_preferences': ['balanced', 'healthy', 'stir_fry'],
        'review_style': 'practical',
        'common_phrases': ['very nice', 'not bad', 'quite good', 'can try']
    },
    {
        'name_pattern': 'indian_male',
        'language_style': 'indian_malaysian_english',
        'food_preferences': ['spicy', 'curry_based', 'vegetarian_friendly'],
        'review_style': 'detailed',
        'common_phrases': ['superb', 'fantastic', 'excellent', 'top class']
    },
    {
        'name_pattern': 'malay_male',
        'language_style': 'casual_malay_english',
        'food_preferences': ['meat_based', 'traditional', 'spicy'],
        'review_style': 'brief_positive',
        'common_phrases': ['power', 'mantap', 'terbaik', 'confirm best']
    },
    {
        'name_pattern': 'chinese_male',
        'language_style': 'chinese_malaysian_english',
        'food_preferences': ['seafood', 'balanced', 'traditional_chinese'],
        'review_style': 'analytical',
        'common_phrases': ['quite nice', 'not bad lah', 'can improve', 'overall good']
    },
    {
        'name_pattern': 'indian_female',
        'language_style': 'indian_malaysian_english',
        'food_preferences': ['vegetarian', 'spicy', 'healthy'],
        'review_style': 'health_conscious',
        'common_phrases': ['very healthy', 'good for family', 'nutritious', 'wholesome']
    }
]

# Review templates by rating level
REVIEW_TEMPLATES = {
    5: {  # Excellent reviews
        'openings': [
            "Wah, this recipe memang terbaik!",
            "Confirm 5 stars! Sedap gila!",
            "Best recipe ever! Family semua suka!",
            "Power lah this recipe!",
            "Memang sedap macam restaurant!"
        ],
        'content_patterns': [
            "Tried this {time_context} and {family_reaction}. The {dish_aspect} is {positive_taste}. {cooking_experience} {recommendation}",
            "{positive_taste} sangat! {texture_comment} and {spice_comment}. {family_context} {cooking_praise}",
            "This recipe {cooking_experience}. {positive_taste} and {texture_comment}. {recommendation} to everyone!"
        ],
        'closings': [
            "Definitely making this again!",
            "Highly recommended!",
            "Memang worth it!",
            "Will share with friends!",
            "Bookmark this recipe!"
        ]
    },
    4: {  # Good reviews
        'openings': [
            "This recipe quite good!",
            "Not bad, family likes it.",
            "Pretty sedap, will try again.",
            "Good recipe, easy to follow.",
            "Quite nice, kids approve."
        ],
        'content_patterns': [
            "Made this {time_context}. {positive_taste} but {minor_issue}. {cooking_experience} Overall {recommendation}",
            "{positive_taste} and {texture_comment}. {spice_comment} {family_context} {cooking_praise}",
            "Recipe is {cooking_praise}. {positive_taste} though {minor_improvement}. {recommendation}"
        ],
        'closings': [
            "Will make again with small changes.",
            "Good recipe overall.",
            "Recommended with modifications.",
            "Worth trying!",
            "Quite satisfied with result."
        ]
    },
    3: {  # Average reviews
        'openings': [
            "This recipe okay lah.",
            "Average je, nothing special.",
            "Biasa-biasa only.",
            "Not bad but can improve.",
            "Okay for first try."
        ],
        'content_patterns': [
            "Tried this recipe {time_context}. {neutral_taste} but {improvement_needed}. {cooking_experience}",
            "{neutral_taste} and {texture_comment}. {spice_comment} Maybe {improvement_suggestion}",
            "Recipe is {cooking_experience}. Result {neutral_taste}. {improvement_needed}"
        ],
        'closings': [
            "Maybe will try again with changes.",
            "Okay for beginners.",
            "Can improve with modifications.",
            "Average recipe.",
            "Not my favorite but okay."
        ]
    },
    2: {  # Below average reviews
        'openings': [
            "This recipe kurang sikit.",
            "Not really my taste.",
            "Disappointing lah.",
            "Expected better.",
            "Tak best sangat."
        ],
        'content_patterns': [
            "Made this {time_context} but {negative_issue}. {negative_taste} and {texture_issue}. {cooking_difficulty}",
            "{negative_taste} and {spice_issue}. {family_reaction_negative} {improvement_needed}",
            "Recipe {cooking_difficulty}. Result {negative_taste}. {disappointment}"
        ],
        'closings': [
            "Won't make again.",
            "Need major improvements.",
            "Not recommended.",
            "Better recipes available.",
            "Disappointing overall."
        ]
    },
    1: {  # Poor reviews
        'openings': [
            "Alamak, this recipe tak jadi.",
            "Very disappointing.",
            "Waste of ingredients.",
            "Don't try this recipe.",
            "Terrible results."
        ],
        'content_patterns': [
            "Tried this recipe but {major_failure}. {very_negative_taste} {cooking_disaster}",
            "{very_negative_taste} and {major_texture_issue}. {family_rejection} {total_disappointment}",
            "Complete disaster! {major_failure} {very_negative_taste} {waste_comment}"
        ],
        'closings': [
            "Total waste of time and ingredients.",
            "Definitely not recommended.",
            "Worst recipe ever tried.",
            "Don't bother with this.",
            "Complete failure."
        ]
    }
}

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        mongo_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client.get_default_database()
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db, client
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None, None

def get_recipe_context(recipe):
    """Extract context from recipe for review generation."""
    cuisine = recipe.get('cuisine', 'International')
    difficulty = recipe.get('difficulty', 'Medium')
    name = recipe.get('name', 'Unknown Recipe')
    
    # Determine dish type and characteristics
    dish_type = 'dish'
    if any(word in name.lower() for word in ['rice', 'nasi', 'fried rice']):
        dish_type = 'rice dish'
    elif any(word in name.lower() for word in ['curry', 'rendang', 'gulai']):
        dish_type = 'curry'
    elif any(word in name.lower() for word in ['soup', 'sup']):
        dish_type = 'soup'
    elif any(word in name.lower() for word in ['chicken', 'ayam']):
        dish_type = 'chicken dish'
    
    return {
        'cuisine': cuisine,
        'difficulty': difficulty,
        'name': name,
        'dish_type': dish_type,
        'is_spicy': any(word in name.lower() for word in ['spicy', 'pedas', 'chili', 'curry']),
        'is_traditional': cuisine in ['Malay', 'Chinese', 'Indian', 'Peranakan']
    }

def select_persona_for_recipe(recipe_context):
    """Select appropriate persona based on recipe characteristics."""
    suitable_personas = []
    
    for persona in MALAYSIAN_PERSONAS:
        # Match food preferences with recipe
        if recipe_context['is_spicy'] and 'spicy' in persona['food_preferences']:
            suitable_personas.append(persona)
        elif recipe_context['cuisine'] == 'Chinese' and 'chinese' in persona['name_pattern']:
            suitable_personas.append(persona)
        elif recipe_context['cuisine'] == 'Indian' and 'indian' in persona['name_pattern']:
            suitable_personas.append(persona)
        elif recipe_context['cuisine'] == 'Malay' and 'malay' in persona['name_pattern']:
            suitable_personas.append(persona)
        else:
            suitable_personas.append(persona)  # All personas can review any recipe
    
    return random.choice(suitable_personas if suitable_personas else MALAYSIAN_PERSONAS)

def generate_review_content(rating, recipe_context, persona):
    """Generate authentic Malaysian review content."""
    templates = REVIEW_TEMPLATES[rating]
    
    # Select components
    opening = random.choice(templates['openings'])
    content_pattern = random.choice(templates['content_patterns'])
    closing = random.choice(templates['closings'])
    
    # Fill in the pattern with contextual content
    content_vars = generate_content_variables(rating, recipe_context, persona)
    
    try:
        content = content_pattern.format(**content_vars)
    except KeyError:
        # Fallback if template variables don't match
        content = f"Made this recipe and it was {random.choice(MALAYSIAN_FOOD_TERMS['taste_positive'] if rating >= 4 else MALAYSIAN_FOOD_TERMS['taste_negative'])}."
    
    # Combine components
    full_review = f"{opening} {content} {closing}"
    
    # Add persona-specific phrases
    if random.random() < 0.3:  # 30% chance to add persona phrase
        phrase = random.choice(persona['common_phrases'])
        full_review += f" {phrase}!"
    
    return full_review

def generate_content_variables(rating, recipe_context, persona):
    """Generate variables for review content templates."""
    variables = {}
    
    # Time context
    time_contexts = ['last weekend', 'yesterday', 'few days ago', 'last week', 'this morning']
    variables['time_context'] = random.choice(time_contexts)
    
    # Taste descriptions based on rating
    if rating >= 4:
        variables['positive_taste'] = random.choice(MALAYSIAN_FOOD_TERMS['taste_positive'])
        variables['neutral_taste'] = random.choice(MALAYSIAN_FOOD_TERMS['taste_positive'])
    elif rating == 3:
        variables['neutral_taste'] = 'okay lah'
        variables['positive_taste'] = 'not bad'
    else:
        variables['negative_taste'] = random.choice(MALAYSIAN_FOOD_TERMS['taste_negative'])
        variables['very_negative_taste'] = 'really ' + random.choice(MALAYSIAN_FOOD_TERMS['taste_negative'])
    
    # Texture and spice comments
    variables['texture_comment'] = f"texture is {random.choice(MALAYSIAN_FOOD_TERMS['texture_terms'])}"
    variables['spice_comment'] = random.choice(MALAYSIAN_FOOD_TERMS['spice_levels']) if recipe_context['is_spicy'] else 'not too spicy'
    
    # Family and cooking context
    variables['family_context'] = random.choice(MALAYSIAN_FOOD_TERMS['family_context'])
    variables['cooking_experience'] = random.choice(MALAYSIAN_FOOD_TERMS['cooking_praise'])
    variables['cooking_praise'] = random.choice(MALAYSIAN_FOOD_TERMS['cooking_praise'])
    
    # Rating-specific variables
    if rating >= 4:
        variables['family_reaction'] = 'everyone loved it'
        variables['recommendation'] = 'Highly recommended'
        variables['dish_aspect'] = recipe_context['dish_type']
    elif rating == 3:
        variables['improvement_needed'] = 'could be better'
        variables['improvement_suggestion'] = 'add more seasoning'
        variables['minor_issue'] = 'a bit bland'
        variables['minor_improvement'] = 'needs more salt'
    else:
        variables['negative_issue'] = 'turned out badly'
        variables['major_failure'] = 'completely failed'
        variables['cooking_difficulty'] = 'was too difficult'
        variables['family_reaction_negative'] = 'family didn\'t like it'
        variables['disappointment'] = 'very disappointing'
        variables['cooking_disaster'] = 'cooking was a disaster'
        variables['major_texture_issue'] = 'texture was terrible'
        variables['family_rejection'] = 'nobody wanted to eat it'
        variables['total_disappointment'] = 'completely disappointed'
        variables['waste_comment'] = 'wasted good ingredients'
        variables['texture_issue'] = 'texture was off'
        variables['spice_issue'] = 'too spicy' if recipe_context['is_spicy'] else 'no flavor'
    
    return variables

def main():
    """Main function for testing review generation."""
    print("🇲🇾 Malaysian Review Generator Test")
    print("=" * 40)
    
    # Test recipe context
    test_recipe = {
        'name': 'Nasi Lemak',
        'cuisine': 'Malay',
        'difficulty': 'Medium'
    }
    
    recipe_context = get_recipe_context(test_recipe)
    print(f"Recipe context: {recipe_context}")
    
    # Generate sample reviews for each rating
    for rating in [5, 4, 3, 2, 1]:
        persona = select_persona_for_recipe(recipe_context)
        review = generate_review_content(rating, recipe_context, persona)
        print(f"\n⭐ {rating}-star review ({persona['name_pattern']}):")
        print(f"'{review}'")

if __name__ == "__main__":
    main()
